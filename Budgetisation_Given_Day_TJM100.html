<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Budgétisation Détaillée - Projet Given Day (TJM 100€)</title>
    <style>
        body {
            font-family: '<PERSON><PERSON><PERSON>', <PERSON><PERSON>, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1100px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9f9f9;
        }
        h1 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
            font-size: 28px;
        }
        h2 {
            color: #2980b9;
            border-left: 4px solid #3498db;
            padding-left: 10px;
            margin-top: 30px;
            font-size: 22px;
        }
        h3 {
            color: #16a085;
            margin-top: 25px;
            font-size: 18px;
        }
        h4 {
            color: #27ae60;
            font-size: 16px;
            margin-top: 20px;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
            box-shadow: 0 2px 3px rgba(0,0,0,0.1);
        }
        th {
            background-color: #3498db;
            color: white;
            text-align: left;
            padding: 12px;
        }
        td {
            border: 1px solid #ddd;
            padding: 10px;
            background-color: white;
        }
        tr:nth-child(even) td {
            background-color: #f2f2f2;
        }
        .highlight {
            background-color: #fffde7;
            padding: 15px;
            border-left: 4px solid #ffd600;
            margin: 15px 0;
        }
        .note {
            background-color: #e3f2fd;
            padding: 15px;
            border-left: 4px solid #2196f3;
            margin: 15px 0;
        }
        .important {
            background-color: #ffebee;
            padding: 15px;
            border-left: 4px solid #f44336;
            margin: 15px 0;
        }
        .success {
            background-color: #e8f5e8;
            padding: 15px;
            border-left: 4px solid #4caf50;
            margin: 15px 0;
        }
        ul, ol {
            padding-left: 25px;
        }
        li {
            margin-bottom: 8px;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            font-style: italic;
            color: #777;
        }
        .emoji {
            font-size: 20px;
            margin-right: 5px;
        }
        .total-row {
            background-color: #2c3e50 !important;
            color: white !important;
            font-weight: bold;
        }
        .phase-header {
            background-color: #34495e !important;
            color: white !important;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>Budgétisation Détaillée - Projet Given Day</h1>

    <div class="highlight">
        <h3>Synthèse budgétaire</h3>
        <p>Ce document présente la budgétisation détaillée du projet Given Day basée sur un Taux Journalier Moyen (TJM) de <strong>100€</strong>, conformément à votre approche de "juste rémunération" et d'analyse locale.</p>
        <p><strong>Coût total estimé : 18 000€ HT</strong> pour le développement du MVP (36 semaines / 180 jours-homme)</p>
    </div>

    <p>Cher Michaël,</p>

    <p>Suite à vos retours et à votre approche budgétaire basée sur le principe de "juste rémunération", nous vous présentons une budgétisation détaillée avec un TJM de 100€, adapté au contexte d'une association chrétienne.</p>

    <h2><span class="emoji">💰</span> Budgétisation par phases</h2>

    <table>
        <tr>
            <th>Phase</th>
            <th>Durée (semaines)</th>
            <th>Jours-homme</th>
            <th>TJM (€)</th>
            <th>Coût (€ HT)</th>
            <th>Description</th>
        </tr>
        <tr class="phase-header">
            <td colspan="6"><strong>PHASE 1 - ANALYSE (TERMINÉE)</strong></td>
        </tr>
        <tr>
            <td>Analyse des besoins</td>
            <td>4</td>
            <td>20</td>
            <td>100</td>
            <td>2 000</td>
            <td>Spécifications, architecture technique</td>
        </tr>
        <tr class="phase-header">
            <td colspan="6"><strong>PHASE 2 - DESIGN UX/UI</strong></td>
        </tr>
        <tr>
            <td>Conception UX/UI</td>
            <td>5</td>
            <td>25</td>
            <td>100</td>
            <td>2 500</td>
            <td>Maquettes, prototypes, charte graphique</td>
        </tr>
        <tr class="phase-header">
            <td colspan="6"><strong>PHASE 3 - DÉVELOPPEMENT BACKEND</strong></td>
        </tr>
        <tr>
            <td>API et base de données</td>
            <td>8</td>
            <td>40</td>
            <td>100</td>
            <td>4 000</td>
            <td>Node.js, PostgreSQL, authentification</td>
        </tr>
        <tr class="phase-header">
            <td colspan="6"><strong>PHASE 4 - DÉVELOPPEMENT MOBILE</strong></td>
        </tr>
        <tr>
            <td>Application React Native</td>
            <td>10</td>
            <td>50</td>
            <td>100</td>
            <td>5 000</td>
            <td>iOS/Android, fonctionnalités MVP</td>
        </tr>
        <tr class="phase-header">
            <td colspan="6"><strong>PHASE 5 - TESTS ET OPTIMISATION</strong></td>
        </tr>
        <tr>
            <td>Tests et corrections</td>
            <td>6</td>
            <td>30</td>
            <td>100</td>
            <td>3 000</td>
            <td>Tests d'intégration, sécurité, performance</td>
        </tr>
        <tr class="phase-header">
            <td colspan="6"><strong>PHASE 6 - DÉPLOIEMENT</strong></td>
        </tr>
        <tr>
            <td>Mise en production</td>
            <td>3</td>
            <td>15</td>
            <td>100</td>
            <td>1 500</td>
            <td>Déploiement, formation, documentation</td>
        </tr>
        <tr class="total-row">
            <td><strong>TOTAL</strong></td>
            <td><strong>36</strong></td>
            <td><strong>180</strong></td>
            <td><strong>100</strong></td>
            <td><strong>18 000</strong></td>
            <td><strong>MVP complet</strong></td>
        </tr>
    </table>

    <h2><span class="emoji">📊</span> Répartition détaillée des coûts</h2>

    <div class="note">
        <h3>Développement technique (80% - 14 400€)</h3>
        <ul>
            <li><strong>Backend (22%)</strong> : 4 000€ - API REST, base de données, authentification</li>
            <li><strong>Frontend Mobile (28%)</strong> : 5 000€ - Application React Native iOS/Android</li>
            <li><strong>Tests et Qualité (17%)</strong> : 3 000€ - Tests, sécurité, optimisation</li>
            <li><strong>Design UX/UI (14%)</strong> : 2 500€ - Interface utilisateur, expérience</li>
        </ul>
    </div>

    <div class="note">
        <h3>Gestion de projet et déploiement (20% - 3 600€)</h3>
        <ul>
            <li><strong>Analyse initiale (11%)</strong> : 2 000€ - Spécifications, architecture</li>
            <li><strong>Déploiement (8%)</strong> : 1 500€ - Mise en production, formation</li>
            <li><strong>Coordination (1%)</strong> : 100€ - Suivi projet, communications</li>
        </ul>
    </div>

    <h2><span class="emoji">💡</span> Fonctionnalités incluses dans le budget</h2>

    <div class="success">
        <h3>Fonctionnalités MVP complètes</h3>
        <ul>
            <li><strong>"Encouragement du Jour Donné"</strong> - Verset/texte quotidien</li>
            <li><strong>Boutons d'action pédagogiques</strong> - Témoigner, prier, partager</li>
            <li><strong>Gestion des utilisateurs</strong> - Inscription, profils, authentification</li>
            <li><strong>Réseautage professionnel</strong> - Connexions, messagerie, recherche</li>
            <li><strong>Groupes et communautés</strong> - Création, gestion, discussions</li>
            <li><strong>Partage de contenu</strong> - Articles, témoignages, versets</li>
            <li><strong>Réactions personnalisées</strong> - Amen, Alléluia, etc.</li>
            <li><strong>Notifications push</strong> - OneSignal intégré</li>
            <li><strong>Sécurité et RGPD</strong> - Conformité complète</li>
            <li><strong>Modération</strong> - Outils de signalement et blocage</li>
        </ul>
    </div>

    <h2><span class="emoji">📅</span> Modalités de paiement proposées</h2>

    <div class="highlight">
        <h3>Échelonnement adapté aux associations</h3>
        <p>Nous proposons un échelonnement de paiement adapté aux contraintes budgétaires d'une association :</p>
        
        <table>
            <tr>
                <th>Échéance</th>
                <th>Montant (€ HT)</th>
                <th>Pourcentage</th>
                <th>Déclencheur</th>
            </tr>
            <tr>
                <td>Signature du contrat</td>
                <td>3 600</td>
                <td>20%</td>
                <td>Lancement du projet</td>
            </tr>
            <tr>
                <td>Fin Phase 2 (Design)</td>
                <td>3 600</td>
                <td>20%</td>
                <td>Validation des maquettes</td>
            </tr>
            <tr>
                <td>Fin Phase 3 (Backend)</td>
                <td>3 600</td>
                <td>20%</td>
                <td>API fonctionnelle</td>
            </tr>
            <tr>
                <td>Fin Phase 4 (Mobile)</td>
                <td>3 600</td>
                <td>20%</td>
                <td>Application mobile complète</td>
            </tr>
            <tr>
                <td>Livraison finale</td>
                <td>3 600</td>
                <td>20%</td>
                <td>Mise en production</td>
            </tr>
        </table>
    </div>

    <h2><span class="emoji">🎯</span> Coûts additionnels (hors développement)</h2>

    <div class="important">
        <h3>Coûts d'infrastructure et services (estimés)</h3>
        <p>Ces coûts ne sont pas inclus dans le développement mais sont nécessaires au fonctionnement :</p>
        
        <table>
            <tr>
                <th>Service</th>
                <th>Coût mensuel (€)</th>
                <th>Coût annuel (€)</th>
                <th>Description</th>
            </tr>
            <tr>
                <td>Hostinger VPS</td>
                <td>15</td>
                <td>180</td>
                <td>Serveur d'hébergement</td>
            </tr>
            <tr>
                <td>Nom de domaine</td>
                <td>1</td>
                <td>12</td>
                <td>givenday.fr ou similaire</td>
            </tr>
            <tr>
                <td>OneSignal (notifications)</td>
                <td>0</td>
                <td>0</td>
                <td>Gratuit jusqu'à 10k utilisateurs</td>
            </tr>
            <tr>
                <td>App Store (iOS)</td>
                <td>8</td>
                <td>99</td>
                <td>Compte développeur Apple</td>
            </tr>
            <tr>
                <td>Google Play (Android)</td>
                <td>-</td>
                <td>25</td>
                <td>Frais unique d'inscription</td>
            </tr>
            <tr class="total-row">
                <td><strong>Total infrastructure</strong></td>
                <td><strong>24</strong></td>
                <td><strong>316</strong></td>
                <td><strong>Première année</strong></td>
            </tr>
        </table>
    </div>

    <div class="note">
        <h3>Coûts de modération (selon vos spécifications)</h3>
        <p>Budget à prévoir pour l'équipe de modération et animation :</p>
        <ul>
            <li><strong>Modérateur de contenu</strong> : À définir selon vos ressources</li>
            <li><strong>Animateur "Encouragement du Jour Donné"</strong> : À définir selon vos ressources</li>
            <li><strong>Équipe de bénévoles</strong> : Production des contenus quotidiens</li>
        </ul>
    </div>

    <h2><span class="emoji">🔄</span> Options d'optimisation budgétaire</h2>

    <div class="highlight">
        <h3>Approches pour réduire les coûts</h3>
        <ul>
            <li><strong>Développement par phases</strong> : Possibilité de développer en plusieurs tranches</li>
            <li><strong>Bénévolat technique</strong> : Intégration de développeurs bénévoles sous supervision</li>
            <li><strong>Partenariats</strong> : Recherche de sponsors techniques chrétiens</li>
            <li><strong>Fonctionnalités différées</strong> : Report de certaines fonctionnalités en V2</li>
        </ul>
    </div>

    <h2><span class="emoji">✅</span> Garanties et support</h2>

    <div class="success">
        <h3>Inclus dans le budget</h3>
        <ul>
            <li><strong>Garantie 3 mois</strong> : Correction des bugs post-livraison</li>
            <li><strong>Formation équipe</strong> : Formation à l'administration de l'application</li>
            <li><strong>Documentation complète</strong> : Guides utilisateur et technique</li>
            <li><strong>Support technique</strong> : 1 mois de support inclus</li>
        </ul>
    </div>

    <h2><span class="emoji">🙏</span> Conclusion</h2>

    <p>Cette budgétisation respecte votre approche de "juste rémunération" tout en garantissant la qualité et la viabilité du projet Given Day. Le TJM de 100€ permet de maintenir un équilibre entre accessibilité pour votre association et rémunération équitable de l'équipe de développement.</p>

    <p>Nous restons flexibles sur les modalités de paiement et ouverts à discuter d'ajustements selon vos contraintes budgétaires et votre campagne de collecte.</p>

    <p>Que Dieu bénisse ce projet et guide nos prochains échanges,</p>

    <p>
        <strong>Manoa Randrianarisoa</strong><br>
        Architecte Technique & Lead Mobile
    </p>

    <div class="footer">
        <p>Budgétisation préparée pour le projet Given Day - TJM 100€</p>
        <p><em>Version 1.0 - Décembre 2024</em></p>
    </div>
</body>
</html>
