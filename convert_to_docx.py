#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de conversion HTML vers DOCX avec préservation du style
"""

import os
import sys
from pathlib import Path

def install_required_packages():
    """Installe les packages nécessaires"""
    packages = ['python-docx', 'beautifulsoup4', 'lxml', 'mammoth']
    
    for package in packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✓ {package} déjà installé")
        except ImportError:
            print(f"Installation de {package}...")
            os.system(f"pip install {package}")

def convert_html_to_docx():
    """Convertit le fichier HTML en DOCX"""
    try:
        from docx import Document
        from docx.shared import Inches, Pt, RGBColor
        from docx.enum.text import WD_ALIGN_PARAGRAPH
        from docx.enum.style import WD_STYLE_TYPE
        from bs4 import BeautifulSoup
        import re
        
        # Lire le fichier HTML
        with open('Budgetisation_Professionnelle_Given_Day.html', 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        # Parser le HTML
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # Créer un nouveau document Word
        doc = Document()
        
        # Configurer les styles de base
        styles = doc.styles
        
        # Style pour les titres principaux
        try:
            title_style = styles['Heading 1']
        except KeyError:
            title_style = styles.add_style('Custom Title', WD_STYLE_TYPE.PARAGRAPH)
        
        title_style.font.name = 'Segoe UI'
        title_style.font.size = Pt(24)
        title_style.font.color.rgb = RGBColor(44, 90, 160)  # Couleur primaire
        title_style.font.bold = True
        
        # Style pour les sous-titres
        try:
            subtitle_style = styles['Heading 2']
        except KeyError:
            subtitle_style = styles.add_style('Custom Subtitle', WD_STYLE_TYPE.PARAGRAPH)
        
        subtitle_style.font.name = 'Segoe UI'
        subtitle_style.font.size = Pt(18)
        subtitle_style.font.color.rgb = RGBColor(44, 90, 160)
        subtitle_style.font.bold = True
        
        # Extraire et ajouter le contenu
        
        # Header
        header_content = soup.find('header')
        if header_content:
            # Logo et titre principal
            logo_section = header_content.find(class_='logo-section')
            if logo_section:
                logo = logo_section.find(class_='logo')
                if logo:
                    p = doc.add_paragraph()
                    run = p.add_run(logo.get_text())
                    run.font.name = 'Segoe UI'
                    run.font.size = Pt(28)
                    run.font.bold = True
                    run.font.color.rgb = RGBColor(44, 90, 160)
                    p.alignment = WD_ALIGN_PARAGRAPH.CENTER
                
                subtitle = logo_section.find(class_='subtitle')
                if subtitle:
                    p = doc.add_paragraph()
                    run = p.add_run(subtitle.get_text())
                    run.font.name = 'Segoe UI'
                    run.font.size = Pt(16)
                    run.font.color.rgb = RGBColor(44, 90, 160)
                    p.alignment = WD_ALIGN_PARAGRAPH.CENTER
            
            # Informations projet
            project_info = header_content.find(class_='project-info')
            if project_info:
                doc.add_paragraph()  # Espace
                for elem in project_info.find_all(['h3', 'p']):
                    p = doc.add_paragraph()
                    run = p.add_run(elem.get_text())
                    if elem.name == 'h3':
                        run.font.bold = True
                        run.font.size = Pt(14)
                    else:
                        run.font.size = Pt(12)
                    p.alignment = WD_ALIGN_PARAGRAPH.RIGHT
        
        # Contenu principal
        container = soup.find(class_='container')
        if container:
            # Titre principal
            main_title = container.find('h1')
            if main_title:
                doc.add_page_break()
                p = doc.add_paragraph()
                # Enlever l'emoji du titre
                title_text = re.sub(r'[^\w\s\-àâäéèêëïîôöùûüÿç]', '', main_title.get_text()).strip()
                run = p.add_run(title_text)
                run.font.name = 'Segoe UI'
                run.font.size = Pt(24)
                run.font.bold = True
                run.font.color.rgb = RGBColor(44, 90, 160)
                p.alignment = WD_ALIGN_PARAGRAPH.CENTER
            
            # Synthèse exécutive
            exec_summary = container.find(class_='executive-summary')
            if exec_summary:
                doc.add_paragraph()
                p = doc.add_paragraph()
                run = p.add_run("SYNTHÈSE EXÉCUTIVE")
                run.font.name = 'Segoe UI'
                run.font.size = Pt(16)
                run.font.bold = True
                run.font.color.rgb = RGBColor(16, 185, 129)
                
                for elem in exec_summary.find_all(['p', 'h3']):
                    if elem.get('class') and 'emoji' in str(elem.get('class')):
                        continue
                    text = elem.get_text().strip()
                    if text and not text.startswith('🎯'):
                        p = doc.add_paragraph()
                        run = p.add_run(text)
                        if elem.name == 'h3':
                            run.font.bold = True
                            run.font.size = Pt(14)
                        else:
                            run.font.size = Pt(11)
            
            # Traiter les sections principales
            for h2 in container.find_all('h2'):
                # Ajouter le titre de section
                doc.add_paragraph()
                p = doc.add_paragraph()
                title_text = re.sub(r'[^\w\s\-àâäéèêëïîôöùûüÿç]', '', h2.get_text()).strip()
                run = p.add_run(title_text)
                run.font.name = 'Segoe UI'
                run.font.size = Pt(18)
                run.font.bold = True
                run.font.color.rgb = RGBColor(44, 90, 160)
                
                # Traiter le contenu suivant jusqu'au prochain h2
                current = h2.next_sibling
                while current and (not hasattr(current, 'name') or current.name != 'h2'):
                    if hasattr(current, 'name'):
                        if current.name == 'h3':
                            p = doc.add_paragraph()
                            title_text = re.sub(r'[^\w\s\-àâäéèêëïîôöùûüÿç]', '', current.get_text()).strip()
                            run = p.add_run(title_text)
                            run.font.name = 'Segoe UI'
                            run.font.size = Pt(14)
                            run.font.bold = True
                            run.font.color.rgb = RGBColor(55, 65, 81)
                        
                        elif current.name == 'p':
                            text = current.get_text().strip()
                            if text:
                                p = doc.add_paragraph()
                                run = p.add_run(text)
                                run.font.size = Pt(11)
                        
                        elif current.name == 'table':
                            # Créer un tableau Word
                            rows = current.find_all('tr')
                            if rows:
                                # Compter les colonnes
                                max_cols = max(len(row.find_all(['th', 'td'])) for row in rows)
                                table = doc.add_table(rows=len(rows), cols=max_cols)
                                table.style = 'Table Grid'
                                
                                for i, row in enumerate(rows):
                                    cells = row.find_all(['th', 'td'])
                                    for j, cell in enumerate(cells):
                                        if j < max_cols:
                                            table_cell = table.cell(i, j)
                                            table_cell.text = cell.get_text().strip()
                                            # Style pour les en-têtes
                                            if cell.name == 'th':
                                                for paragraph in table_cell.paragraphs:
                                                    for run in paragraph.runs:
                                                        run.font.bold = True
                                                        run.font.color.rgb = RGBColor(255, 255, 255)
                        
                        elif current.name in ['div'] and current.get('class'):
                            classes = current.get('class')
                            if 'note' in classes or 'warning' in classes or 'success' in classes:
                                text = current.get_text().strip()
                                if text:
                                    p = doc.add_paragraph()
                                    run = p.add_run(text)
                                    run.font.size = Pt(11)
                                    if 'warning' in classes:
                                        run.font.color.rgb = RGBColor(245, 158, 11)
                                    elif 'success' in classes:
                                        run.font.color.rgb = RGBColor(5, 150, 105)
                            
                            elif 'budget-highlight' in classes:
                                for elem in current.find_all(['h3', 'div', 'p']):
                                    text = elem.get_text().strip()
                                    if text:
                                        p = doc.add_paragraph()
                                        run = p.add_run(text)
                                        if 'budget-amount' in str(elem.get('class', [])):
                                            run.font.size = Pt(20)
                                            run.font.bold = True
                                            run.font.color.rgb = RGBColor(44, 90, 160)
                                            p.alignment = WD_ALIGN_PARAGRAPH.CENTER
                                        else:
                                            run.font.size = Pt(11)
                                            p.alignment = WD_ALIGN_PARAGRAPH.CENTER
                    
                    current = current.next_sibling
        
        # Footer
        footer_content = soup.find(class_='footer')
        if footer_content:
            doc.add_page_break()
            for p_elem in footer_content.find_all('p'):
                text = p_elem.get_text().strip()
                if text:
                    p = doc.add_paragraph()
                    run = p.add_run(text)
                    run.font.size = Pt(11)
                    run.font.color.rgb = RGBColor(107, 114, 128)
                    p.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # Sauvegarder le document
        output_file = 'Budgetisation_Professionnelle_Given_Day.docx'
        doc.save(output_file)
        print(f"✓ Document converti avec succès : {output_file}")
        
        return True
        
    except Exception as e:
        print(f"Erreur lors de la conversion : {e}")
        return False

if __name__ == "__main__":
    print("=== Conversion HTML vers DOCX ===")
    print("Installation des dépendances...")
    install_required_packages()
    
    print("\nConversion en cours...")
    if convert_html_to_docx():
        print("\n✓ Conversion terminée avec succès !")
        print("Le fichier 'Budgetisation_Professionnelle_Given_Day.docx' a été créé.")
    else:
        print("\n✗ Échec de la conversion.")