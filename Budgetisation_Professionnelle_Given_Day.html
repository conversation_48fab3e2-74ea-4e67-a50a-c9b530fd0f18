<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Budgétisation - Given Day</title>
    <style>
        :root {
            --primary-color: #2c5aa0;
            --secondary-color: #f8fafc;
            --accent-color: #10b981;
            --text-color: #1f2937;
            --light-text: #6b7280;
            --border-color: #e5e7eb;
            --warning-color: #f59e0b;
            --success-color: #059669;
            --header-font: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            --body-font: '<PERSON><PERSON><PERSON>', Arial, sans-serif;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: var(--body-font);
            line-height: 1.6;
            color: var(--text-color);
            background-color: #ffffff;
            padding: 0;
            margin: 0;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        header {
            background: linear-gradient(135deg, var(--primary-color), #1e40af);
            color: white;
            padding: 40px 0;
            margin-bottom: 40px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }

        .logo-section {
            flex: 1;
        }

        .logo {
            font-size: 36px;
            font-weight: bold;
            font-family: var(--header-font);
            margin-bottom: 8px;
        }

        .subtitle {
            font-size: 18px;
            opacity: 0.9;
        }

        .project-info {
            text-align: right;
            flex: 1;
        }

        .project-info h3 {
            margin-bottom: 5px;
            font-size: 16px;
        }

        .project-info p {
            font-size: 14px;
            opacity: 0.8;
        }

        h1, h2, h3, h4 {
            font-family: var(--header-font);
            margin-bottom: 20px;
            color: var(--primary-color);
        }

        h1 {
            font-size: 32px;
            margin-bottom: 30px;
            border-bottom: 3px solid var(--primary-color);
            padding-bottom: 15px;
        }

        h2 {
            font-size: 26px;
            margin-top: 50px;
            border-left: 5px solid var(--primary-color);
            padding-left: 20px;
            background-color: var(--secondary-color);
            padding: 15px 20px;
            border-radius: 8px;
        }

        h3 {
            font-size: 22px;
            margin-top: 35px;
            color: #374151;
        }

        .executive-summary {
            background: linear-gradient(135deg, #eff6ff, #dbeafe);
            border-left: 5px solid var(--accent-color);
            padding: 30px;
            margin: 30px 0;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
        }

        .budget-highlight {
            background-color: #f0f9ff;
            border: 2px solid #0ea5e9;
            padding: 25px;
            margin: 25px 0;
            border-radius: 10px;
            text-align: center;
        }

        .budget-highlight h3 {
            color: #0c4a6e;
            margin-bottom: 15px;
        }

        .budget-amount {
            font-size: 28px;
            font-weight: bold;
            color: var(--primary-color);
            margin: 10px 0;
        }

        .note {
            background-color: var(--secondary-color);
            border-left: 4px solid var(--primary-color);
            padding: 20px;
            margin: 25px 0;
            border-radius: 6px;
        }

        .warning {
            background-color: #fffbeb;
            border-left: 4px solid var(--warning-color);
            padding: 20px;
            margin: 25px 0;
            border-radius: 6px;
        }

        .success {
            background-color: #ecfdf5;
            border-left: 4px solid var(--success-color);
            padding: 20px;
            margin: 25px 0;
            border-radius: 6px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin: 25px 0;
            background-color: white;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-radius: 10px;
            overflow: hidden;
        }

        th {
            background-color: var(--primary-color);
            color: white;
            text-align: left;
            padding: 15px 12px;
            font-weight: 600;
        }

        td {
            padding: 12px;
            border-bottom: 1px solid var(--border-color);
            vertical-align: top;
        }

        tr:last-child td {
            border-bottom: none;
        }

        tr:nth-child(even) {
            background-color: #f9fafb;
        }

        .total-row {
            background-color: #e0f2fe !important;
            font-weight: bold;
            border-top: 2px solid var(--primary-color);
        }

        .mvp-row {
            background-color: #f0f9ff !important;
        }

        .v2-row {
            background-color: #f3f4f6 !important;
        }

        .priority-high {
            background-color: #fee2e2;
            color: #991b1b;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }

        .priority-medium {
            background-color: #fef3c7;
            color: #92400e;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }

        .timeline {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin: 30px 0;
        }

        .timeline-item {
            display: flex;
            align-items: center;
            padding: 15px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-left: 4px solid var(--accent-color);
        }

        .timeline-date {
            min-width: 120px;
            font-weight: bold;
            color: var(--primary-color);
        }

        .timeline-content {
            flex: 1;
            margin-left: 20px;
        }

        .comparison-table {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
            margin: 30px 0;
        }

        .comparison-card {
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
        }

        .comparison-card h4 {
            color: var(--primary-color);
            margin-bottom: 15px;
        }

        .price {
            font-size: 24px;
            font-weight: bold;
            color: var(--accent-color);
            margin: 10px 0;
        }

        .footer {
            background-color: var(--primary-color);
            color: white;
            padding: 40px 0;
            margin-top: 60px;
            text-align: center;
        }

        .footer p {
            margin: 8px 0;
        }

        .emoji {
            font-size: 22px;
            margin-right: 10px;
        }

        @media screen and (max-width: 768px) {
            .container {
                padding: 0 15px;
            }

            .header-content {
                flex-direction: column;
                text-align: center;
            }

            .project-info {
                text-align: center;
                margin-top: 20px;
            }

            .comparison-table {
                grid-template-columns: 1fr;
            }

            table {
                display: block;
                overflow-x: auto;
                white-space: nowrap;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="header-content">
                <div class="logo-section">
                    <div class="logo">Given Day</div>
                    <div class="subtitle">Budgétisation MVP & V2</div>
                </div>
                <div class="project-info">
                    <h3>Chef de Projet & Architecte Technique</h3>
                    <p>Juin 2024</p>
                    <p>Lancement prévu : Janvier 2026</p>
                </div>
            </div>
        </div>
    </header>

    <div class="container">
        <h1><span class="emoji">📊</span>Budgétisation - Given Day</h1>

        <div class="executive-summary">
            <h3><span class="emoji">🎯</span>Synthèse Exécutive</h3>
            <p><strong>Suite à l'analyse de vos retours détaillés, nous présentons une budgétisation complète et transparente du projet Given Day.</strong></p>
            <p>Cette proposition respecte vos contraintes temporelles (lancement janvier 2026) et budgétaires, tout en intégrant l'ensemble des fonctionnalités spécifiques demandées.</p>
            
            <div class="note">
                <h3>Équipe Confirmée - 5 Développeurs</h3>
                <p><strong>Équipe disponible dès juin 2025 :</strong> Chef de Projet & Architecte Mobile, Développeur Backend, Designer UX/UI, Développeur Frontend Web, Développeur Support</p>
                <p><strong>Planning adapté :</strong> 7 mois de développement intensif pour respecter le lancement janvier 2026</p>
            </div>
        </div>

        <h2><span class="emoji">🚀</span>MVP - Budget Détaillé (Priorité Absolue)</h2>

        <div class="note">
            <h3>Périmètre MVP Finalisé</h3>
            <p>Le MVP intègre toutes les fonctionnalités spécifiques demandées dans vos retours :</p>
            <ul style="margin-top: 10px; margin-left: 20px;">
                <li><strong>Encouragement du Jour Donné</strong> avec système de gestion</li>
                <li><strong>Boutons d'action pédagogiques</strong> (Témoigner, Prier, Révélation)</li>
                <li><strong>Réactions personnalisées</strong> (Amen, Alléluia, God is with you, Preach it)</li>
                <li><strong>Système de modération</strong> adapté au contenu spirituel</li>
            </ul>
        </div>

        <div class="budget-highlight">
            <h3>Budget MVP Confirmé</h3>
            <div class="budget-amount">17 415€ HT</div>
            <p><em>204 jours-homme • TJM différencié 60-100€ • Équipe 5 personnes</em></p>
        </div>

        <h3>Répartition par Fonctionnalités MVP</h3>
        <table>
            <tr>
                <th>Fonctionnalité</th>
                <th>Description</th>
                <th>Responsable</th>
                <th>Durée (jours)</th>
                <th>Coût (€ HT)</th>
                <th>Priorité</th>
            </tr>
            <tr class="mvp-row">
                <td><strong>Authentification & Profils</strong></td>
                <td>Inscription, connexion, gestion profils chrétiens</td>
                <td>Backend + Mobile</td>
                <td>25</td>
                <td>2 000</td>
                <td><span class="priority-high">HAUTE</span></td>
            </tr>
            <tr class="mvp-row">
                <td><strong>Encouragement du Jour Donné</strong></td>
                <td>Système quotidien + interface admin web/mobile</td>
                <td>Backend + Mobile + Web</td>
                <td>18</td>
                <td>1 440</td>
                <td><span class="priority-high">HAUTE</span></td>
            </tr>
            <tr class="mvp-row">
                <td><strong>Boutons d'Action Pédagogiques</strong></td>
                <td>3 boutons spécialisés + workflow</td>
                <td>Mobile + Backend</td>
                <td>15</td>
                <td>1 200</td>
                <td><span class="priority-high">HAUTE</span></td>
            </tr>
            <tr class="mvp-row">
                <td><strong>Réactions Personnalisées</strong></td>
                <td>4 réactions chrétiennes spécifiques</td>
                <td>Mobile + Backend</td>
                <td>12</td>
                <td>960</td>
                <td><span class="priority-high">HAUTE</span></td>
            </tr>
            <tr class="mvp-row">
                <td><strong>Publications & Partage</strong></td>
                <td>Système de posts, images, versets</td>
                <td>Backend + Mobile</td>
                <td>30</td>
                <td>2 400</td>
                <td><span class="priority-high">HAUTE</span></td>
            </tr>
            <tr class="mvp-row">
                <td><strong>Groupes de Prière</strong></td>
                <td>Création, gestion, participation</td>
                <td>Backend + Mobile</td>
                <td>28</td>
                <td>2 240</td>
                <td><span class="priority-high">HAUTE</span></td>
            </tr>
            <tr class="mvp-row">
                <td><strong>Interface Admin Web Complète</strong></td>
                <td>Dashboard modération + gestion contenu</td>
                <td>Frontend Web + Backend</td>
                <td>22</td>
                <td>1 760</td>
                <td><span class="priority-high">HAUTE</span></td>
            </tr>
            <tr class="mvp-row">
                <td><strong>Notifications Push</strong></td>
                <td>Encouragements, prières, interactions</td>
                <td>Backend + Mobile</td>
                <td>15</td>
                <td>1 200</td>
                <td><span class="priority-medium">MOYENNE</span></td>
            </tr>
            <tr class="mvp-row">
                <td><strong>Recherche & Découverte</strong></td>
                <td>Utilisateurs, groupes, contenus</td>
                <td>Backend + Mobile</td>
                <td>20</td>
                <td>1 600</td>
                <td><span class="priority-medium">MOYENNE</span></td>
            </tr>
            <tr class="mvp-row">
                <td><strong>Tests & Qualité</strong></td>
                <td>Tests automatisés + validation</td>
                <td>Équipe complète</td>
                <td>19</td>
                <td>1 520</td>
                <td><span class="priority-high">HAUTE</span></td>
            </tr>
            <tr class="total-row">
                <td colspan="3"><strong>TOTAL MVP</strong></td>
                <td><strong>204 jours</strong></td>
                <td><strong>17 415€ HT</strong></td>
                <td>-</td>
            </tr>
        </table>

        <h3>Répartition par Équipe & TJM Différencié</h3>

        <div class="note">
            <h3>TJM Adapté par Expertise</h3>
            <p>Selon les normes du marché et l'expertise requise, nous appliquons un TJM différencié tout en maintenant l'approche solidaire :</p>
        </div>

        <table>
            <tr>
                <th>Membre Équipe</th>
                <th>Niveau d'Expertise</th>
                <th>TJM (€)</th>
                <th>Jours Alloués</th>
                <th>Tâches Principales</th>
                <th>Coût (€ HT)</th>
            </tr>
            <tr>
                <td><strong>Chef de Projet & Architecte Mobile</strong></td>
                <td>Senior + Management</td>
                <td>100</td>
                <td>55 jours</td>
                <td>Gestion projet + App mobile + Architecture</td>
                <td>5 500</td>
            </tr>
            <tr>
                <td><strong>Développeur Backend</strong></td>
                <td>Senior</td>
                <td>90</td>
                <td>50 jours</td>
                <td>Backend + API + Sécurité + Déploiement</td>
                <td>4 500</td>
            </tr>
            <tr>
                <td><strong>Designer UX/UI</strong></td>
                <td>Senior</td>
                <td>85</td>
                <td>35 jours</td>
                <td>Maquettes mobile/web + Prototypes + Identité</td>
                <td>2 975</td>
            </tr>
            <tr>
                <td><strong>Développeur Frontend Web</strong></td>
                <td>Confirmé</td>
                <td>75</td>
                <td>40 jours</td>
                <td>Admin web + Dashboard + Modération + Responsive</td>
                <td>3 000</td>
            </tr>
            <tr>
                <td><strong>Développeur Support</strong></td>
                <td>Junior/Confirmé</td>
                <td>60</td>
                <td>24 jours</td>
                <td>Tests + QA + Support développement + Débogage</td>
                <td>1 440</td>
            </tr>
            <tr class="total-row">
                <td colspan="3"><strong>TOTAL ÉQUIPE</strong></td>
                <td><strong>204 jours</strong></td>
                <td><strong>Développement complet MVP</strong></td>
                <td><strong>17 415€ HT</strong></td>
            </tr>
        </table>

        <div class="success">
            <h3>Justification TJM Différencié</h3>
            <ul style="margin-top: 15px; margin-left: 20px;">
                <li><strong>Chef de Projet (100€) :</strong> Responsabilité management + architecture + mobile</li>
                <li><strong>Backend Senior (90€) :</strong> Complexité API + sécurité + infrastructure</li>
                <li><strong>Designer Senior (85€) :</strong> Expertise UX/UI + identité visuelle</li>
                <li><strong>Frontend Confirmé (75€) :</strong> Interface admin + responsive</li>
                <li><strong>Support Junior (60€) :</strong> Tests + QA + assistance développement</li>
            </ul>
        </div>

        <h3>Planning par Phases - Juin 2025 à Janvier 2026</h3>
        <table>
            <tr>
                <th>Phase</th>
                <th>Période Réelle</th>
                <th>Durée</th>
                <th>Équipe Mobilisée</th>
                <th>Livrables Principaux</th>
            </tr>
            <tr style="background-color: #e8f5e8;">
                <td><strong>Phase 1 - Analyse Finale</strong></td>
                <td>Mai 2025 (Terminée)</td>
                <td>4 semaines</td>
                <td>Chef de Projet</td>
                <td>Spécifications détaillées + Architecture <strong>(OFFERTE)</strong></td>
            </tr>
            <tr>
                <td><strong>Phase 2 - Design UX/UI</strong></td>
                <td>Juin 2025 (S2-S6)</td>
                <td>5 semaines</td>
                <td>Designer + Chef de Projet</td>
                <td>Maquettes Mobile + Web + Prototypes</td>
            </tr>
            <tr>
                <td><strong>Phase 3 - Développement Backend</strong></td>
                <td>Juillet-Août 2025</td>
                <td>8 semaines</td>
                <td>Backend + Support</td>
                <td>API + Base de données + Sécurité</td>
            </tr>
            <tr>
                <td><strong>Phase 4 - Développement Frontend</strong></td>
                <td>Septembre-Octobre 2025</td>
                <td>8 semaines</td>
                <td>Mobile + Web + Support</td>
                <td>App Mobile + Interface Admin</td>
            </tr>
            <tr>
                <td><strong>Phase 5 - Intégration & Tests</strong></td>
                <td>Novembre 2025</td>
                <td>4 semaines</td>
                <td>Équipe complète</td>
                <td>Tests + Corrections + Optimisations</td>
            </tr>
            <tr>
                <td><strong>Phase 6 - Déploiement</strong></td>
                <td>Décembre 2025</td>
                <td>4 semaines</td>
                <td>Backend + Chef de Projet</td>
                <td>Mise en production + Formation</td>
            </tr>
            <tr class="total-row">
                <td colspan="2"><strong>LANCEMENT</strong></td>
                <td><strong>Janvier 2026</strong></td>
                <td><strong>Support équipe</strong></td>
                <td><strong>🚀 Given Day en production</strong></td>
            </tr>
        </table>

        <h3>Architecture Mutualisée Mobile/Web - Économie Maximale</h3>

        <div class="success">
            <h3>🔄 Fonctionnalités Partagées Mobile/Web</h3>
            <p><strong>Même fonctionnalités, interfaces différentes :</strong></p>

            <table style="margin-top: 15px;">
                <tr>
                    <th>Fonctionnalité</th>
                    <th>Mobile (React Native)</th>
                    <th>Web Admin (React)</th>
                    <th>Backend Partagé</th>
                </tr>
                <tr>
                    <td><strong>Authentification</strong></td>
                    <td>Interface mobile optimisée</td>
                    <td>Interface desktop admin</td>
                    <td>✅ API commune</td>
                </tr>
                <tr>
                    <td><strong>Encouragement du Jour</strong></td>
                    <td>Affichage + interactions</td>
                    <td>Gestion + publication</td>
                    <td>✅ Même système</td>
                </tr>
                <tr>
                    <td><strong>Boutons d'Action</strong></td>
                    <td>Interface utilisateur</td>
                    <td>Modération + validation</td>
                    <td>✅ Même workflow</td>
                </tr>
                <tr>
                    <td><strong>Réactions Personnalisées</strong></td>
                    <td>Interface tactile</td>
                    <td>Statistiques + modération</td>
                    <td>✅ Même logique</td>
                </tr>
                <tr>
                    <td><strong>Groupes de Prière</strong></td>
                    <td>Participation mobile</td>
                    <td>Administration + modération</td>
                    <td>✅ API partagée</td>
                </tr>
                <tr>
                    <td><strong>Publications</strong></td>
                    <td>Création + consultation</td>
                    <td>Modération + gestion</td>
                    <td>✅ Même base données</td>
                </tr>
            </table>
        </div>

        <div class="note">
            <h3>💡 Économies Réalisées par la Mutualisation</h3>
            <ul style="margin-top: 15px; margin-left: 20px;">
                <li><strong>Backend unique :</strong> Pas de duplication API (économie ~8 000€)</li>
                <li><strong>Base de données partagée :</strong> Même structure, pas de synchronisation</li>
                <li><strong>Designer unique :</strong> Adaptation responsive vs design séparé</li>
                <li><strong>Tests mutualisés :</strong> Logique métier testée une fois</li>
                <li><strong>Maintenance simplifiée :</strong> Un seul backend à maintenir</li>
            </ul>
        </div>

        <h2><span class="emoji">🔮</span>V2 - Estimation Budgétaire (Post-MVP)</h2>

        <div class="warning">
            <h3>Fonctionnalités V2 - Estimation avec Même Équipe</h3>
            <p><strong>Important :</strong> Cette estimation est basée sur les fonctionnalités actuellement identifiées avec la même équipe de 5 développeurs.</p>
            <p><strong>Déclenchement V2 :</strong> Après atteinte des 1 000 utilisateurs et validation des métriques d'engagement.</p>
            <p><strong>Équipe V2 :</strong> Même équipe confirmée + expérience acquise sur le projet = efficacité accrue.</p>
        </div>

        <table>
            <tr>
                <th>Fonctionnalité V2</th>
                <th>Description</th>
                <th>Complexité</th>
                <th>Estimation (jours)</th>
                <th>Coût Estimé (€ HT)</th>
            </tr>
            <tr class="v2-row">
                <td><strong>Intégration Hello Bible</strong></td>
                <td>API + synchronisation ressources</td>
                <td>Élevée</td>
                <td>20</td>
                <td>1 600</td>
            </tr>
            <tr class="v2-row">
                <td><strong>Intégration YouVersion</strong></td>
                <td>Plans de lecture + synchronisation</td>
                <td>Élevée</td>
                <td>25</td>
                <td>2 000</td>
            </tr>
            <tr class="v2-row">
                <td><strong>Système de Dons</strong></td>
                <td>Paiements sécurisés + gestion</td>
                <td>Élevée</td>
                <td>30</td>
                <td>2 400</td>
            </tr>
            <tr class="v2-row">
                <td><strong>Événements & Calendrier</strong></td>
                <td>Gestion événements églises/ONG</td>
                <td>Moyenne</td>
                <td>25</td>
                <td>2 000</td>
            </tr>
            <tr class="v2-row">
                <td><strong>Analytics Avancés</strong></td>
                <td>Tableaux de bord + métriques</td>
                <td>Moyenne</td>
                <td>18</td>
                <td>1 440</td>
            </tr>
            <tr class="v2-row">
                <td><strong>Messagerie Privée</strong></td>
                <td>Chat 1-to-1 + groupes privés</td>
                <td>Moyenne</td>
                <td>22</td>
                <td>1 760</td>
            </tr>
            <tr class="v2-row">
                <td><strong>Contenu Multimédia</strong></td>
                <td>Vidéos + streaming + podcasts</td>
                <td>Élevée</td>
                <td>16</td>
                <td>1 280</td>
            </tr>
            <tr class="total-row">
                <td colspan="3"><strong>TOTAL V2 (Estimation)</strong></td>
                <td><strong>156 jours</strong></td>
                <td><strong>12 480€ HT</strong></td>
            </tr>
        </table>

        <h2><span class="emoji">👥</span>Budget Modération & Animation (Séparé)</h2>

        <div class="warning">
            <h3>Ressources Humaines - Budget Additionnel</h3>
            <p><strong>Important :</strong> Ces coûts ne sont pas inclus dans le budget de développement et constituent un budget opérationnel séparé :</p>
        </div>

        <table>
            <tr>
                <th>Poste</th>
                <th>Profil</th>
                <th>Temps Estimé</th>
                <th>Coût Mensuel (€)</th>
                <th>Coût Annuel (€)</th>
            </tr>
            <tr>
                <td><strong>Modérateur Principal</strong></td>
                <td>Temps partiel (20h/semaine)</td>
                <td>Supervision générale + validation contenu</td>
                <td>600</td>
                <td>7 200</td>
            </tr>
            <tr>
                <td><strong>Animateur Encouragement</strong></td>
                <td>Temps partiel (10h/semaine)</td>
                <td>Gestion quotidienne + coordination bénévoles</td>
                <td>300</td>
                <td>3 600</td>
            </tr>
            <tr>
                <td><strong>Équipe Bénévoles</strong></td>
                <td>5-10 contributeurs</td>
                <td>Production contenu spirituel</td>
                <td>0</td>
                <td>0</td>
            </tr>
            <tr class="total-row">
                <td><strong>TOTAL MODÉRATION</strong></td>
                <td colspan="2"><strong>Budget opérationnel annuel</strong></td>
                <td><strong>900€</strong></td>
                <td><strong>10 800€</strong></td>
            </tr>
        </table>

        <div class="note">
            <h3>Flexibilité Budgétaire</h3>
            <p>Ces montants sont adaptables selon :</p>
            <ul style="margin-top: 10px; margin-left: 20px;">
                <li><strong>Volume d'activité :</strong> Ajustement selon le nombre d'utilisateurs</li>
                <li><strong>Ressources disponibles :</strong> Possibilité de commencer avec moins</li>
                <li><strong>Bénévolat :</strong> Réduction possible si plus de bénévoles disponibles</li>
                <li><strong>Évolution progressive :</strong> Montée en charge selon la croissance</li>
            </ul>
        </div>

        <h2><span class="emoji">🖥️</span>Frais d'Infrastructure & Hébergement</h2>

        <h3>Coûts Mensuels & Annuels</h3>
        <table>
            <tr>
                <th>Service</th>
                <th>Coût Mensuel (€)</th>
                <th>Coût Annuel (€)</th>
                <th>Description</th>
                <th>Évolution</th>
            </tr>
            <tr>
                <td><strong>Serveur Hostinger VPS</strong></td>
                <td>15</td>
                <td>180</td>
                <td>4 CPU, 8GB RAM, 160GB SSD</td>
                <td>Suffisant jusqu'à 1000 utilisateurs</td>
            </tr>
            <tr>
                <td><strong>Nom de domaine</strong></td>
                <td>1</td>
                <td>12</td>
                <td>givenday.fr + certificat SSL</td>
                <td>Fixe</td>
            </tr>
            <tr>
                <td><strong>OneSignal (Notifications)</strong></td>
                <td>0</td>
                <td>0</td>
                <td>Gratuit jusqu'à 10k utilisateurs</td>
                <td>Payant après 10k utilisateurs</td>
            </tr>
            <tr>
                <td><strong>Apple Developer</strong></td>
                <td>8</td>
                <td>99</td>
                <td>Compte développeur iOS</td>
                <td>Fixe annuel</td>
            </tr>
            <tr>
                <td><strong>Google Play Console</strong></td>
                <td>-</td>
                <td>25</td>
                <td>Frais unique d'inscription</td>
                <td>Paiement unique</td>
            </tr>
            <tr>
                <td><strong>Backup & Sécurité</strong></td>
                <td>5</td>
                <td>60</td>
                <td>Sauvegardes automatiques</td>
                <td>Recommandé</td>
            </tr>
            <tr class="total-row">
                <td><strong>TOTAL Année 1</strong></td>
                <td><strong>29€</strong></td>
                <td><strong>376€</strong></td>
                <td colspan="2">Infrastructure complète</td>
            </tr>
        </table>

        <h3>Projection Infrastructure 3 ans</h3>
        <table>
            <tr>
                <th>Année</th>
                <th>Utilisateurs Estimés</th>
                <th>Infrastructure</th>
                <th>Coût Annuel (€)</th>
                <th>Évolution</th>
            </tr>
            <tr>
                <td><strong>Année 1 (2026)</strong></td>
                <td>300 → 900</td>
                <td>Hostinger VPS</td>
                <td>376</td>
                <td>Configuration initiale</td>
            </tr>
            <tr>
                <td><strong>Année 2 (2027)</strong></td>
                <td>900 → 3000</td>
                <td>Migration DigitalOcean</td>
                <td>720</td>
                <td>Montée en charge</td>
            </tr>
            <tr>
                <td><strong>Année 3 (2028)</strong></td>
                <td>3000 → 15000</td>
                <td>Infrastructure scalable</td>
                <td>1440</td>
                <td>Croissance soutenue</td>
            </tr>
        </table>

        <h2><span class="emoji">📅</span>Planning Détaillé jusqu'à Janvier 2026</h2>

        <div class="success">
            <h3>Respect de la Contrainte Temporelle</h3>
            <p>Le planning ci-dessous garantit un lancement en <strong>janvier 2026</strong> avec toutes les fonctionnalités MVP intégrées et testées.</p>
        </div>

        <h3>📅 Planning Calendaire Détaillé - Workflow Parallèle Optimisé</h3>

        <div class="success">
            <h3>🚀 Stratégie de Développement Parallèle</h3>
            <p><strong>Gain de temps maximal :</strong> Backend démarre dès validation design, Frontend dès premiers endpoints disponibles</p>
        </div>

        <style>
            .calendar-grid {
                display: grid;
                grid-template-columns: 120px repeat(7, 1fr);
                gap: 2px;
                margin: 20px 0;
                background-color: #f0f0f0;
                border-radius: 8px;
                padding: 10px;
            }
            .calendar-header {
                background-color: var(--primary-color);
                color: white;
                padding: 8px;
                text-align: center;
                font-weight: bold;
                font-size: 12px;
            }
            .calendar-month {
                background-color: var(--primary-color);
                color: white;
                padding: 8px;
                text-align: center;
                font-weight: bold;
                writing-mode: vertical-lr;
                text-orientation: mixed;
            }
            .calendar-cell {
                background-color: white;
                padding: 6px;
                text-align: center;
                font-size: 11px;
                min-height: 60px;
                border-radius: 4px;
                position: relative;
            }
            .task-bar {
                position: absolute;
                left: 2px;
                right: 2px;
                height: 12px;
                border-radius: 2px;
                font-size: 9px;
                color: white;
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: bold;
            }
            .task-design { background-color: #9c27b0; }
            .task-backend { background-color: #2196f3; }
            .task-mobile { background-color: #4caf50; }
            .task-web { background-color: #ff9800; }
            .task-test { background-color: #f44336; }
            .task-deploy { background-color: #607d8b; }
            .milestone {
                background-color: #ffd700;
                color: #333;
                font-weight: bold;
                border: 2px solid #ffb300;
            }
        </style>

        <div class="calendar-grid">
            <div class="calendar-header">Équipe</div>
            <div class="calendar-header">Juin S2</div>
            <div class="calendar-header">Juin S3-4</div>
            <div class="calendar-header">Juillet</div>
            <div class="calendar-header">Août</div>
            <div class="calendar-header">Sept</div>
            <div class="calendar-header">Oct</div>
            <div class="calendar-header">Nov-Déc</div>

            <div class="calendar-month">Designer</div>
            <div class="calendar-cell">
                <div class="task-bar task-design" style="top: 5px;">Wireframes</div>
            </div>
            <div class="calendar-cell">
                <div class="task-bar task-design" style="top: 5px;">Maquettes</div>
                <div class="task-bar milestone" style="top: 25px;">J1 Validé</div>
            </div>
            <div class="calendar-cell">
                <div class="task-bar task-design" style="top: 5px;">Web Design</div>
            </div>
            <div class="calendar-cell">
                <div style="padding: 5px; font-size: 10px;">Support équipe</div>
            </div>
            <div class="calendar-cell">
                <div style="padding: 5px; font-size: 10px;">Ajustements</div>
            </div>
            <div class="calendar-cell">
                <div style="padding: 5px; font-size: 10px;">Finitions</div>
            </div>
            <div class="calendar-cell">
                <div style="padding: 5px; font-size: 10px;">Support</div>
            </div>

            <div class="calendar-month">Backend</div>
            <div class="calendar-cell">
                <div style="padding: 5px; font-size: 10px;">Préparation</div>
            </div>
            <div class="calendar-cell">
                <div class="task-bar task-backend" style="top: 5px;">DB + Auth</div>
            </div>
            <div class="calendar-cell">
                <div class="task-bar task-backend" style="top: 5px;">API Core</div>
                <div class="task-bar milestone" style="top: 25px;">Endpoints</div>
            </div>
            <div class="calendar-cell">
                <div class="task-bar task-backend" style="top: 5px;">API Complète</div>
                <div class="task-bar milestone" style="top: 25px;">J2 API</div>
            </div>
            <div class="calendar-cell">
                <div class="task-bar task-backend" style="top: 5px;">Fonctions Spé</div>
            </div>
            <div class="calendar-cell">
                <div class="task-bar task-backend" style="top: 5px;">Optimisation</div>
            </div>
            <div class="calendar-cell">
                <div class="task-bar task-deploy" style="top: 5px;">Déploiement</div>
            </div>

            <div class="calendar-month">Mobile</div>
            <div class="calendar-cell">
                <div style="padding: 5px; font-size: 10px;">Attente Design</div>
            </div>
            <div class="calendar-cell">
                <div style="padding: 5px; font-size: 10px;">Préparation</div>
            </div>
            <div class="calendar-cell">
                <div style="padding: 5px; font-size: 10px;">Attente API</div>
            </div>
            <div class="calendar-cell">
                <div class="task-bar task-mobile" style="top: 5px;">App Base</div>
            </div>
            <div class="calendar-cell">
                <div class="task-bar task-mobile" style="top: 5px;">Fonctions MVP</div>
            </div>
            <div class="calendar-cell">
                <div class="task-bar task-mobile" style="top: 5px;">Intégration</div>
                <div class="task-bar milestone" style="top: 25px;">J3 MVP</div>
            </div>
            <div class="calendar-cell">
                <div class="task-bar task-test" style="top: 5px;">Tests</div>
            </div>

            <div class="calendar-month">Web Frontend</div>
            <div class="calendar-cell">
                <div style="padding: 5px; font-size: 10px;">Attente Design</div>
            </div>
            <div class="calendar-cell">
                <div style="padding: 5px; font-size: 10px;">Préparation</div>
            </div>
            <div class="calendar-cell">
                <div style="padding: 5px; font-size: 10px;">Attente API</div>
            </div>
            <div class="calendar-cell">
                <div class="task-bar task-web" style="top: 5px;">Admin Base</div>
            </div>
            <div class="calendar-cell">
                <div class="task-bar task-web" style="top: 5px;">Dashboard</div>
            </div>
            <div class="calendar-cell">
                <div class="task-bar task-web" style="top: 5px;">Modération</div>
            </div>
            <div class="calendar-cell">
                <div class="task-bar task-test" style="top: 5px;">Tests</div>
            </div>

            <div class="calendar-month">Support/QA</div>
            <div class="calendar-cell">
                <div style="padding: 5px; font-size: 10px;">Veille</div>
            </div>
            <div class="calendar-cell">
                <div style="padding: 5px; font-size: 10px;">Support</div>
            </div>
            <div class="calendar-cell">
                <div class="task-bar task-test" style="top: 5px;">Tests API</div>
            </div>
            <div class="calendar-cell">
                <div class="task-bar task-test" style="top: 5px;">Tests Apps</div>
            </div>
            <div class="calendar-cell">
                <div class="task-bar task-test" style="top: 5px;">QA Complète</div>
            </div>
            <div class="calendar-cell">
                <div class="task-bar task-test" style="top: 5px;">Tests Finaux</div>
                <div class="task-bar milestone" style="top: 25px;">J4 Beta</div>
            </div>
            <div class="calendar-cell">
                <div class="task-bar task-deploy" style="top: 5px;">Go Live</div>
                <div class="task-bar milestone" style="top: 25px;">J6 🚀</div>
            </div>
        </div>

        <div class="note">
            <h3>Légende du Planning</h3>
            <div style="display: flex; flex-wrap: wrap; gap: 15px; margin-top: 10px;">
                <div style="display: flex; align-items: center;"><div style="width: 20px; height: 12px; background-color: #9c27b0; margin-right: 8px;"></div>Design UX/UI</div>
                <div style="display: flex; align-items: center;"><div style="width: 20px; height: 12px; background-color: #2196f3; margin-right: 8px;"></div>Backend/API</div>
                <div style="display: flex; align-items: center;"><div style="width: 20px; height: 12px; background-color: #4caf50; margin-right: 8px;"></div>Mobile</div>
                <div style="display: flex; align-items: center;"><div style="width: 20px; height: 12px; background-color: #ff9800; margin-right: 8px;"></div>Web Frontend</div>
                <div style="display: flex; align-items: center;"><div style="width: 20px; height: 12px; background-color: #f44336; margin-right: 8px;"></div>Tests/QA</div>
                <div style="display: flex; align-items: center;"><div style="width: 20px; height: 12px; background-color: #ffd700; border: 1px solid #ffb300; margin-right: 8px;"></div>Jalons</div>
            </div>
        </div>

        <div class="success">
            <h3>Gestion de Projet</h3>
            <ul style="margin-top: 15px; margin-left: 20px;">
                <li><strong>Sprints de 2 semaines :</strong> Méthode agile avec livrables réguliers</li>
                <li><strong>Chevauchements optimisés :</strong> Backend démarre dès semaine 4 (gain de temps)</li>
                <li><strong>Suivi hebdomadaire :</strong> Points d'avancement chaque vendredi</li>
                <li><strong>Trello en temps réel :</strong> Visibilité complète sur toutes les tâches</li>
                <li><strong>Tests continus :</strong> Validation à chaque sprint</li>
                <li><strong>Gestion des imprévus :</strong> Buffer intégré + plans de contingence</li>
                <li><strong>Communication transparente :</strong> Disponibilité totale équipe</li>
            </ul>
        </div>

        <h2><span class="emoji">💳</span>Modalités de Paiement & Flexibilité</h2>

        <div class="note">
            <h3>Approche Adaptée au Contexte Associatif</h3>
            <p>Nous comprenons que votre association fonctionne avec des dons et des ressources variables. Nous proposons plusieurs options de paiement pour s'adapter à vos contraintes :</p>
        </div>

        <h3>Options de Paiement Proposées</h3>
        <table>
            <tr>
                <th>Option</th>
                <th>Description</th>
                <th>Avantages</th>
                <th>Modalités</th>
            </tr>
            <tr>
                <td><strong>Paiement par Phases</strong></td>
                <td>5 échéances liées aux livrables</td>
                <td>Paiement selon l'avancement réel</td>
                <td>3 483€ par phase validée</td>
            </tr>
            <tr>
                <td><strong>Paiement Mensuel</strong></td>
                <td>Étalement sur 8 mois</td>
                <td>Lissage de la charge financière</td>
                <td>2 177€/mois (juin 2025 - janvier 2026)</td>
            </tr>
            <tr>
                <td><strong>Paiement Mixte</strong></td>
                <td>Acompte + mensualités</td>
                <td>Flexibilité maximale</td>
                <td>À convenir selon vos préférences</td>
            </tr>
        </table>

        <div class="warning">
            <h3>Acompte à la Signature</h3>
            <p><strong>Montant à convenir ensemble</strong> selon vos possibilités financières actuelles.</p>
            <p>Nous privilégions une approche collaborative plutôt qu'un pourcentage fixe, en accord avec l'esprit de votre mission.</p>
        </div>

        <h2><span class="emoji">🌍</span>Comparaison Coûts Internationaux</h2>

        <div class="comparison-table">
            <div class="comparison-card">
                <h4>Marché Français Standard</h4>
                <div class="price">400-600€/jour</div>
                <p>TJM développeur senior</p>
                <p><strong>Coût projet :</strong> 81 600€ - 122 400€</p>
                <p><em>5-7x plus cher</em></p>
            </div>
            <div class="comparison-card">
                <h4>Offshore Classique</h4>
                <div class="price">200-300€/jour</div>
                <p>TJM équipe offshore</p>
                <p><strong>Coût projet :</strong> 40 800€ - 61 200€</p>
                <p><em>2,5-3,5x plus cher</em></p>
            </div>
            <div class="comparison-card">
                <h4>Notre Approche Given Day</h4>
                <div class="price">80€/jour</div>
                <p>TJM solidaire adapté</p>
                <p><strong>Coût projet :</strong> 16 320€</p>
                <p><em>Juste rémunération</em></p>
            </div>
        </div>

        <div class="success">
            <h3>Économie Réalisée pour l'Association</h3>
            <p>Notre approche solidaire vous fait économiser <strong>65 000€ à 106 000€</strong> par rapport aux tarifs du marché français, tout en garantissant une qualité professionnelle et un accompagnement personnalisé adapté aux associations.</p>

            <h4>Détail des Économies Réalisées</h4>
            <table style="margin-top: 15px;">
                <tr>
                    <th>Poste d'Économie</th>
                    <th>Économie Réalisée</th>
                    <th>Justification</th>
                </tr>
                <tr>
                    <td><strong>Analyse & Spécifications</strong></td>
                    <td>3 200€</td>
                    <td>Phase d'analyse offerte (40 jours × 80€)</td>
                </tr>
                <tr>
                    <td><strong>TJM Solidaire vs Marché</strong></td>
                    <td>63 185€</td>
                    <td>Écart TJM moyen marché (400€) vs notre TJM moyen (85€)</td>
                </tr>
                <tr>
                    <td><strong>Gestion de Projet Optimisée</strong></td>
                    <td>2 100€</td>
                    <td>Processus agiles + équipe expérimentée</td>
                </tr>
                <tr>
                    <td><strong>Architecture Mutualisée</strong></td>
                    <td>8 000€</td>
                    <td>Web inclus dans MVP (pas de développement séparé)</td>
                </tr>
                <tr style="background-color: #e8f5e8; font-weight: bold;">
                    <td><strong>TOTAL ÉCONOMIES</strong></td>
                    <td><strong>76 485€</strong></td>
                    <td><strong>Approche solidaire & optimisée</strong></td>
                </tr>
            </table>
        </div>

        <h2><span class="emoji">📋</span>Jalons de Validation Client</h2>

        <table>
            <tr>
                <th>Jalon</th>
                <th>Date Prévue</th>
                <th>Livrables</th>
                <th>Validation Requise</th>
                <th>Critères d'Acceptation</th>
            </tr>
            <tr>
                <td><strong>J1 - Design</strong></td>
                <td>Fin Juin 2025</td>
                <td>Maquettes Mobile + Web + Prototypes</td>
                <td>Validation UX/UI</td>
                <td>Ergonomie + Identité visuelle + Fonctionnalités spécifiques</td>
            </tr>
            <tr>
                <td><strong>J2 - API Backend</strong></td>
                <td>Fin Août 2025</td>
                <td>API fonctionnelle + Base données + Sécurité</td>
                <td>Tests techniques</td>
                <td>Performance + Sécurité + Fonctionnalités complètes</td>
            </tr>
            <tr>
                <td><strong>J3 - Application Mobile</strong></td>
                <td>Fin Octobre 2025</td>
                <td>App iOS/Android + Interface Admin Web</td>
                <td>Tests utilisateurs</td>
                <td>Fonctionnalités + Ergonomie + Intégration API</td>
            </tr>
            <tr>
                <td><strong>J4 - Tests Utilisateurs</strong></td>
                <td>Fin Novembre 2025</td>
                <td>Version beta complète testée</td>
                <td>Tests d'acceptation</td>
                <td>Stabilité + Performance + Expérience utilisateur</td>
            </tr>
            <tr>
                <td><strong>J5 - Recette Finale</strong></td>
                <td>Fin Décembre 2025</td>
                <td>Version production prête</td>
                <td>Recette fonctionnelle</td>
                <td>Conformité totale + Formation équipe</td>
            </tr>
            <tr>
                <td><strong>J6 - Lancement</strong></td>
                <td>Janvier 2026</td>
                <td>Given Day en production</td>
                <td>Mise en service</td>
                <td>Disponibilité + Performance + Support utilisateurs</td>
            </tr>
        </table>

        <h2><span class="emoji">⚠️</span>Gestion des Risques</h2>

        <table>
            <tr>
                <th>Risque Identifié</th>
                <th>Probabilité</th>
                <th>Impact</th>
                <th>Mitigation</th>
                <th>Plan de Contingence</th>
            </tr>
            <tr>
                <td><strong>Retard Phase Design</strong></td>
                <td>Faible</td>
                <td>Moyen</td>
                <td>Validation rapide client + Designer expérimenté</td>
                <td>Parallélisation avec début backend</td>
            </tr>
            <tr>
                <td><strong>Complexité Backend</strong></td>
                <td>Moyenne</td>
                <td>Élevé</td>
                <td>Architecture éprouvée + Tests continus</td>
                <td>Développeur support + Réduction périmètre non-critique</td>
            </tr>
            <tr>
                <td><strong>Indisponibilité Équipe</strong></td>
                <td>Faible</td>
                <td>Élevé</td>
                <td>Équipe confirmée + Engagement contractuel</td>
                <td>Développeur de remplacement + Réorganisation tâches</td>
            </tr>
            <tr>
                <td><strong>Dérive Fonctionnelle</strong></td>
                <td>Moyenne</td>
                <td>Moyen</td>
                <td>Périmètre figé + Validation jalons</td>
                <td>Avenant budgétaire + Report V2</td>
            </tr>
            <tr>
                <td><strong>Problème Technique Majeur</strong></td>
                <td>Faible</td>
                <td>Élevé</td>
                <td>Technologies éprouvées + Tests précoces</td>
                <td>Solution alternative + Extension délai</td>
            </tr>
        </table>

        <h2><span class="emoji">🎯</span>Garanties & Engagements</h2>

        <div class="note">
            <h3>Nos Engagements Qualité</h3>
            <ul style="margin-top: 15px; margin-left: 20px;">
                <li><strong>Respect des délais :</strong> Lancement garanti en janvier 2026 avec équipe de 5 développeurs</li>
                <li><strong>Qualité technique :</strong> Tests automatisés + validation continue + Code review</li>
                <li><strong>Conformité fonctionnelle :</strong> 100% des fonctionnalités spécifiées + Tests utilisateurs</li>
                <li><strong>Support post-lancement :</strong> 3 mois d'assistance technique inclus</li>
                <li><strong>Formation équipe :</strong> Formation administration + Documentation utilisateur</li>
                <li><strong>Maintenance corrective :</strong> 6 mois de corrections de bugs inclus</li>
                <li><strong>Évolutivité :</strong> Architecture préparée pour les intégrations V2</li>
                <li><strong>Sécurité :</strong> Audit sécurité + Conformité RGPD</li>
            </ul>
        </div>

        <div class="warning">
            <h3>Conditions de Révision</h3>
            <p><strong>Périmètre figé :</strong> Ce budget est basé sur le périmètre fonctionnel défini. Toute modification substantielle fera l'objet d'un avenant.</p>
            <p><strong>Évolutions mineures :</strong> Les ajustements mineurs sont inclus dans le cadre de notre collaboration.</p>
        </div>

        <h2><span class="emoji">📞</span>Prochaines Étapes</h2>

        <div class="success">
            <h3>Actions Immédiates - Juin 2025</h3>
            <ol style="margin-top: 15px; margin-left: 25px;">
                <li><strong>Validation de cette proposition budgétaire</strong> (Semaine 1)</li>
                <li><strong>Choix des modalités de paiement</strong> selon vos préférences</li>
                <li><strong>Signature du contrat de développement</strong> (Semaine 1)</li>
                <li><strong>Confirmation disponibilité équipe 5 développeurs</strong> (Immédiat)</li>
                <li><strong>Lancement Phase 2 - Design</strong> (Semaine 2 de juin 2025)</li>
                <li><strong>Mise en place outils projet</strong> (Trello, communication, reporting)</li>
                <li><strong>Premier point d'avancement</strong> (Vendredi semaine 2)</li>
            </ol>
        </div>

        <div class="budget-highlight">
            <h3>Récapitulatif Budgétaire Final</h3>
            <div class="budget-amount">MVP (Web inclus) : 17 415€ HT</div>
            <div class="budget-amount">V2 (estimation) : 12 480€ HT</div>
            <p><em>Équipe 5 développeurs • TJM différencié 60-100€ • Planning 7 mois • Lancement janvier 2026</em></p>
        </div>

        <div class="executive-summary">
            <h3><span class="emoji">🤝</span>Engagement de l'Équipe</h3>
            <p>En tant que <strong>Chef de Projet & Architecte Technique</strong>, je m'engage personnellement à la réussite de Given Day.</p>
            <p>Ce projet représente bien plus qu'un développement technique : c'est la concrétisation d'une vision qui peut transformer la vie de milliers de chrétiens francophones.</p>
            <p><strong>Nous sommes prêts à commencer dès votre validation.</strong></p>
        </div>

    </div>

    <div class="footer">
        <div class="container">
            <p><strong>Budgétisation - Given Day</strong></p>
            <p>Architecte Technique</p>
            <p><em>"Chaque jour est un jour donné par Dieu — un Given Day — pour aimer, servir et construire avec sens."</em></p>
        </div>
    </div>
</body>
</html>
